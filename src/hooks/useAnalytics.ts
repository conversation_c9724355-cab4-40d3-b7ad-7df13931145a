import analytics, { FirebaseAnalyticsTypes } from '@react-native-firebase/analytics';
import { useEffect, useState } from 'react';

export const analyticsEventType = {
  predefined: "predefined",
  custom: "custom"
};

/**
 * Hook for Firebase Analytics that only works in production environment.
 * In non-production environments (development, UAT), the hook will still return
 * the same interface but won't actually initialize analytics or log events.
 */
export const useAnalytics = () => {
  const [appAnalytics, setAppAnalytics] = useState<ReturnType<typeof analytics> | null>(null);
  // Use our environment utility to determine if we're in production
  const productionEnv = false;

  // Log the current environment for debugging
  useEffect(() => {
    async function logEnvironment() {
      if (productionEnv) {
        setAppAnalytics(analytics());
      }
      else {
        await analytics().setAnalyticsCollectionEnabled(productionEnv);
      }
    }

    logEnvironment();
  }, [productionEnv]);

  const setAnalyticsEvent = async (
    eventType: string,
    eventData: FirebaseAnalyticsTypes.EventParams | FirebaseAnalyticsTypes.SelectContentEventParameters
  ) => {
    // Only fire analytics events in production environment
    if (appAnalytics && productionEnv) {
      try {
        if (eventType === analyticsEventType.predefined) {
          await appAnalytics.logSelectContent(eventData as FirebaseAnalyticsTypes.SelectContentEventParameters);
        } else {
          await appAnalytics.logEvent(eventData.item_id as string, eventData as FirebaseAnalyticsTypes.EventParams);
        }
      } catch (error) {
        console.error('Analytics logEvent error:', error);
      }
    }
  };
  const logScreenView = async (currentRouteName: string | undefined) => {
    if (!currentRouteName && !productionEnv) {
      return
    }
    await analytics()?.logScreenView({
      screen_name: currentRouteName,
      screen_class: currentRouteName,
    });
  }

  const logSignUpStart = async () => {
    await setAnalyticsEvent(analyticsEventType.custom, {
      item_id: "sign_up_start",
      action: "true",
    });
  };

  return { setAnalyticsEvent, logScreenView, logSignUpStart };
};