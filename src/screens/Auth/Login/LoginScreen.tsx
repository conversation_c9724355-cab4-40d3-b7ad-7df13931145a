import { Pressable, TouchableOpacity, View } from "react-native";
import { useTranslation } from "react-i18next";
import React, { useCallback, useLayoutEffect, useState } from "react";

import { useAppDispatch, useAppSelector } from "@/store";
import Common from "@/theme/common.style";
import { SafeScreen } from "@/components/template";
import Icons from "@/theme/assets/images/svgs/icons";
import { Button, Typography } from "@/components/atoms";
import { createUserThunk, setLoading } from "@/store/slices/userSlice";

import getLoginStyle from "./LoginScreen.style";
import Loading from "@/components/atoms/Loading/Loading";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { AuthNavProp, LoginScreenRouteProp } from "@/types/navigation";
import { useAuth0Authentication } from "@/hooks/useAuth0Authentication";
import { AUTH_PROVIDER_LIST } from "@/constants/authProviders";
import Each from "@/components/atoms/Each/Each";
import GenericModal from "@/components/molecules/GenericModal/GenericModal";
import { useNavigation } from "@react-navigation/native";
import { login, setIdToken } from "@/store/slices/authSlice";
import { useAuth0 } from "react-native-auth0";
import { useTheme } from "@/theme";
import { initializeTimezoneThunk, setLastLoginMethod } from "@/store/slices/settingsSlice";
import { AUTH_METHOD, selectAuthMethod, setSelectedAuthMethod } from "@/store/slices/onboardingSlice";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import { AnalyticsFirebase } from "@/constants";

type Props = {
  route: LoginScreenRouteProp;
};

const LoginScreen: React.FC<Props> = ({ route }) => {
  const { clearCredentials } = useAuth0();
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { t } = useTranslation(["login"]);
  const { loginWithConnection } = useAuth0Authentication();
  const navigation = useNavigation<AuthNavProp>();
  const styles: any = useDynamicStyles(getLoginStyle);
  const { variant } = useTheme();
  const { setAnalyticsEvent } = useAnalytics();

  const signUpStartAnalytics = useCallback(() => (
    setAnalyticsEvent(analyticsEventType.custom,
      AnalyticsFirebase.CUSTOM_EVENTS.SIGN_UP_START(
        AnalyticsFirebase.actionBool.TRUE)
    )), [])

  const [modalConfig, setModalConfig] = useState<{
    visible: boolean;
    headerText: string;
    confirmText: string;
    closeText?: string;
    onConfirm: () => void;
  }>({
    visible: false,
    headerText: "",
    confirmText: "",
    closeText: "Close",
    onConfirm: () => { },
  });
  const isSignup = route?.params?.isSignup;
  const loginWithOption = useCallback(
    (connection: string, label: string) => async () => {
      setIsLoading(true);
      try {
        const result = await loginWithConnection(connection, label, isSignup, "LOGIN SCREEN");
        if (result?.alreadyExists) {
          setModalConfig({
            visible: true,
            headerText: "You already have \n an account.",
            confirmText: "Login",
            closeText: "Choose Another Sign up Method",
            onConfirm: () => {
              const payloadForUser = {
                email: result?.decodedToken?.email || "",
                name: result?.decodedToken?.name || "",
                nickname: result?.decodedToken?.nickname || "",
                userSubId: result?.decodedToken?.sub || "",
                phoneNumber: result?.decodedToken?.phone || "",
              };

              dispatch(createUserThunk(payloadForUser))
                .unwrap()
                .then((res) => {
                  // if (res && res.onBoarded && TimezoneService.isEmptyOrUTC(res.timeZoneId))
                  if (res && res.onBoarded) {
                    dispatch(initializeTimezoneThunk())
                  }
                });
              dispatch(setIdToken(result?.idToken));
              dispatch(login(result?.user));
              dispatch(setLastLoginMethod(label.toLowerCase()));
              setModalConfig((prev) => ({ ...prev, visible: false }));
            },
          });
        }

        if (result?.noAccountCreated) {
          setModalConfig({
            visible: true,
            headerText: "You do not have \n an account with us.",
            confirmText: "Create Account",
            closeText: "Close",
            onConfirm: () => {
              signUpStartAnalytics()
              setModalConfig((prev) => ({ ...prev, visible: false }));
              navigation.navigate("InitialScreen"); // Adjust this to your intended signup screen
            },
          });
        }
      } catch (e) {
        console.error("Login failed:", e);
      } finally {
        setIsLoading(false);
      }
    },
    [isSignup]
  );

  useLayoutEffect(() => {
    dispatch(setLoading(false));
  }, []);
  const navigationToDesiredScreens = () => {
    dispatch(setSelectedAuthMethod(!isSignup ? AUTH_METHOD.SIGN_UP : AUTH_METHOD.LOGIN));
    if (!isSignup) {
      signUpStartAnalytics()
      navigation.navigate("DisclaimerScreen", { isSignup: true });
    } else {
      navigation.push("LoginScreen", { isSignup: false });
    }
  }
  return (
    <SafeScreen>
      <View style={styles.container}>
        <Icons.Logo style={styles.logo} fill={styles.logoFill.color} />
        <Typography.H0 style={styles.loginText}>
          {isSignup ? "Sign up" : "Login"}
        </Typography.H0>

        <View style={styles.btnGroupView}>
          <Each
            of={AUTH_PROVIDER_LIST}
            render={({ connection, icon, label }, index) => (
              <Button.Auth
                key={index}
                onPress={loginWithOption(connection, label)}
                disabled={isLoading}
              >
                <View style={Common.rowJustifyCenter}>
                  <View style={styles.baseView}>
                    {icon}
                    <Typography.B1 style={styles.authText}>
                      {t("continueWith", { connection: label })}
                    </Typography.B1>
                  </View>
                </View>
              </Button.Auth>
            )}
          />
        </View>
        <View style={styles.bottomTextView}>
          <Typography.B3 style={styles.bottomText}>
            {isSignup ? "Already have an account? " : "Dont have an account? "}
          </Typography.B3>
          <TouchableOpacity
            onPress={navigationToDesiredScreens}
          >
            <Typography.B3 style={styles.loginLinkText}>
              {isSignup ? "Log in" : "Sign up"}
            </Typography.B3>
          </TouchableOpacity>
        </View>

      </View>

      <GenericModal
        isVisible={modalConfig.visible}
        onConfirm={modalConfig.onConfirm}
        onClose={() => {
          clearCredentials();
          setModalConfig((prev) => ({ ...prev, visible: false }));
        }}
        headerText={modalConfig.headerText}
        confirmText={modalConfig.confirmText}
        closeText={modalConfig.closeText}
        svgSource={variant === "dark" ? <Icons.Warning width={40} height={40} /> : <Icons.WarningLight width={40} height={40} />}
        customModalStyle={{
          padding: "0@ms",
        }}
        customModalHeaderStyle={styles.modalHeaderStyle}
        customButtonGroupStyle={styles.modalCustomGroupStyle}
      />

      {!isLoading ? null : <Loading />}
    </SafeScreen>
  );
};

export default LoginScreen;
