/** @format */

import { View } from "react-native";
import React, { useCallback, useState } from "react";

import { store, useAppSelector } from "@/store";
import { SafeScreen } from "@/components/template";
import Icons from "@/theme/assets/images/svgs/icons";
import { Button, Typography } from "@/components/atoms";

import getInitialStyle from "./InitialScreen.styles";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { useTheme } from "@/theme";
import LottieView from "lottie-react-native";
import animationWhite from "@/theme/assets/lotties/initital_screen_animation_white_text.lottie";
import animationBlack from "@/theme/assets/lotties/initial_screen_animation_black_text.lottie";
import BottomAuthSection from "@/components/molecules/BottomAuthSection/BottomAuthSection";
import { selectLastLoginMethod } from "@/store/slices/settingsSlice";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { AuthNavProp } from "@/types/navigation";
import Loading from "@/components/atoms/Loading/Loading";
import { AUTH_METHOD, setSelectedAuthMethod } from "@/store/slices/onboardingSlice";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import { AnalyticsFirebase } from "@/constants";

const InitialScreen: React.FC = () => {
  const navigation = useNavigation<AuthNavProp>();
  const lastLoginMethod = useAppSelector(selectLastLoginMethod);
  const { variant } = useTheme();
  const styles: any = useDynamicStyles(getInitialStyle);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { setAnalyticsEvent } = useAnalytics();

  useFocusEffect(useCallback(() => {
    store.dispatch(setSelectedAuthMethod(AUTH_METHOD.LOGIN));
  }, []))

  const onLoginPress = () => {
    store.dispatch(setSelectedAuthMethod(AUTH_METHOD.LOGIN));
    navigation.navigate("LoginScreen", { isSignup: false });
  };
  const onSignupPress = () => {
    setAnalyticsEvent(analyticsEventType.custom,
      AnalyticsFirebase.CUSTOM_EVENTS.SIGN_UP_START(
        AnalyticsFirebase.actionBool.TRUE)
    );
    store.dispatch(setSelectedAuthMethod(AUTH_METHOD.SIGN_UP));
    navigation.navigate("DisclaimerScreen", { isSignup: true });
  };
  return (
    <SafeScreen>
      <View style={styles.topSection}>
        <Icons.WideRibbonIcon width={"100%"} style={styles.ribbon} />
        <Icons.Logo style={styles.logo} fill={styles.logoFill.color} />
      </View>
      <View style={styles.container}>
        <View style={styles.middleSection}>
          <View style={styles.imagePlaceholder}>
            <LottieView
              source={variant === "dark" ? animationWhite : animationBlack}
              style={styles.lottieView}
              autoPlay
              loop
            />
          </View>
        </View>
        {lastLoginMethod ? (
          <BottomAuthSection
            lastUsedAuthMethod={lastLoginMethod}
            onLoginPress={onLoginPress}
            onSwitchMethod={onLoginPress}
            onSignupPress={onSignupPress}
            setIsLoading={setIsLoading}
            isLoading={isLoading}
          />
        ) : (
          <View style={styles.bottomSection}>
            <Button.Main style={{ width: "100%" }} onPress={onLoginPress}>
              <Typography.B1 style={styles.loginButtonText}>Login</Typography.B1>
            </Button.Main>

            <Typography.B3 style={styles.subText}>Don’t have an account yet?</Typography.B3>

            <Button.YellowOutline onPress={onSignupPress} style={styles.createAccountButton}>
              <Typography.B1 style={styles.createButtonText}>Create an acount</Typography.B1>
            </Button.YellowOutline>
          </View>
        )}
      </View>
      {!isLoading ? null : <Loading />}
    </SafeScreen>
  );
};

export default InitialScreen;
