import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, ScrollView, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  getAllowanceLabel,
  getAllowancePlaceholder,
  getPheAllowanceUnit,
} from "@/utils/helpers";
import { useRemoteConfig } from "@/context/RemoteConfigContext";
import { selectDietTracker } from "@/store/slices/dietTrackerSlice";
import { UserOnboarding } from "@/types/schemas/user";
import { Button, Typography } from "@/components/atoms";
import { useAppDispatch, useAppSelector } from "@/store";
import { getOnboardingStyle } from "./Onboarding.style";
import InputField from "@/components/molecules/Field/InputField";
import RNCheckbox from "@/components/atoms/RNCheckbox/RNCheckbox";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import TabSelector from "@/components/atoms/TabSelector/TabSelector";
import {
  NEW_ONBOARDING_CREATE_ACCOUNT_SCHEMA,
} from "@/shared/input-validations/onboarding-user-validation";
import SimplifiedDietToggle from "@/components/molecules/SimplifiedDietToggle";
import {
  selectConsumptionUnit,
  setConsumptionUnit,
} from "@/store/slices/settingsSlice";
import { addVersionManually } from "@/utils/versionStorage";
import DeviceInfo from "react-native-device-info";
import Common from "@/theme/common.style";
import { AUTH_METHOD, setAccountData, setSelectedAuthMethod } from "@/store/slices/onboardingSlice";
import { AuthNavProp } from "@/types/navigation";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import { AnalyticsFirebase } from "@/constants";

const CreateAccount = () => {
  const navigation = useNavigation<AuthNavProp>();
  const { t } = useTranslation(["onBoardingUser"]);
  const dispatch = useAppDispatch();
  const consumptionType = useAppSelector(selectConsumptionUnit);
  const [isSimplifiedDiet, setIsSimplifiedDiet] = useState(false);
  const { remoteConfig } = useRemoteConfig();
  const [selectedConsumption, setSelectedConsumption] = useState<string>("Phe");
  const { setAnalyticsEvent } = useAnalytics();

  const styles: any = useDynamicStyles(getOnboardingStyle);

  const { control, handleSubmit, trigger } = useForm<UserOnboarding>({
    mode: "all",
    resolver: yupResolver(NEW_ONBOARDING_CREATE_ACCOUNT_SCHEMA),
    context: { consumptionType },
    defaultValues: {
      agreedToTerms: false,
      dataUsageConsent: false,
      subscribedToUpdates: false,
    },
  });

  useEffect(() => {
    dispatch(setConsumptionUnit("Phe"));
    let mounted = true;
    if (mounted) {
      addVersionManually(DeviceInfo.getVersion());
    }
    return () => {
      mounted = false;
    };
  }, []);

  const handleContinue = (data: UserOnboarding) => {
    const updatedData = {
      ...data,
      isSimplifiedDiet,
    };
    setAnalyticsEvent(analyticsEventType.custom,
      AnalyticsFirebase.CUSTOM_EVENTS.SIGN_UP_START(
        AnalyticsFirebase.actionBool.TRUE
      )
    );
    dispatch(setAccountData(updatedData));
    dispatch(setSelectedAuthMethod(AUTH_METHOD.SIGN_UP));
    navigation.navigate("LoginScreen", { isSignup: true });
    // navigation.navigate("UserProfile", { accountData: updatedData });
  };

  function handleTermsAndConditions() {
    Linking.openURL(remoteConfig.linkTermsAndConditions);
  }

  const renderUnit = () => {
    return (
      <Typography.B2 style={styles.unit}>
        {getPheAllowanceUnit(consumptionType)}
      </Typography.B2>
    );
  };

  const onConsumptionTabSelector = (type: string) => {
    setSelectedConsumption(type);
    dispatch(setConsumptionUnit(type as "Phe" | "Protein"));
    setTimeout(() => {
      trigger("pheAllowance");
    }, 0);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.createAccountScrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={Common.justifySpaceFlex}>
          <View>
            <Typography.H3 style={[styles.createAccountHeaderTitle]}>
              {"Create Account"}
            </Typography.H3>
            {/* <Header onBackPress={() => {}} title="Create Account" /> */}
            <Typography.B2 style={styles.labelText}>
              {t("trackConsumption")}
            </Typography.B2>
            <View style={{ width: "70%" }}>
              <TabSelector
                style={styles.tabBg}
                tabs={["Phe", "Protein"]}
                selectedTab={selectedConsumption}
                onTabPress={(type) => {
                  onConsumptionTabSelector(type);
                }}
              />
            </View>

            {/* phe allowance */}
            <InputField
              name="pheAllowance"
              control={control}
              RightChild={renderUnit()}
              label={t(getAllowanceLabel(consumptionType))}
              placeholder={t(getAllowancePlaceholder(consumptionType))}
              keyboardType="decimal-pad"
            />

            <SimplifiedDietToggle
              isToggleOn={isSimplifiedDiet}
              onToggle={() => setIsSimplifiedDiet(!isSimplifiedDiet)}
              disabled={false}
              loading={false}
            />

            <InputField
              name="agreedToTerms"
              control={control}
              label={t("agreeTermsAndConditions")}
              label2={t("termsAndConditions")}
              label2Press={handleTermsAndConditions}
              component={RNCheckbox}
              errorStyle={{ marginTop: -20, marginLeft: 32 }}
              trigger="onSelect"
            />

            <InputField
              name="subscribedToUpdates"
              control={control}
              label={t("futureUpdates")}
              component={RNCheckbox}
              trigger="onSelect"
            />

            <InputField
              name="dataUsageConsent"
              control={control}
              label={t("userDataInsights")}
              component={RNCheckbox}
              errorStyle={{ marginTop: -2, marginLeft: 32 }}
              trigger="onSelect"
            />
          </View>
          <View>
            <Button.Yellow
              style={styles.createAccountBtnConatiner}
              onPress={handleSubmit(handleContinue)}
              activeOpacity={0.85}
            >
              <Typography.B2 style={styles.btnText}>Continue</Typography.B2>
            </Button.Yellow>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default CreateAccount;
